@import url('https://fonts.googleapis.com/css2?family=Raleway:wght@400;500;600;700&display=swap');

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Raleway', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* Dashboard Layout */
.body {
    display: flex;
    min-height: 100vh;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    z-index: 1000;
}

.u-name {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.u-name b {
    color: #667eea;
}

/* Sidebar Styles */
.side-bar {
    position: fixed;
    left: 0;
    top: 60px;
    width: 250px;
    height: calc(100vh - 60px);
    background: #2c3e50;
    transition: all 0.3s ease;
    z-index: 999;
}

.user-p {
    padding: 30px 20px;
    text-align: center;
    border-bottom: 1px solid #34495e;
}

.user-p img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid #667eea;
    margin-bottom: 15px;
}

.user-p h4 {
    color: #ecf0f1;
    font-size: 18px;
    font-weight: 500;
}

/* Navigation Styles */
.side-bar ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.side-bar ul li {
    border-bottom: 1px solid #34495e;
}

.side-bar ul li a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 16px;
}

.side-bar ul li a:hover {
    background: #34495e;
    color: #fff;
    padding-left: 30px;
}

.side-bar ul li a.active {
    background: #667eea;
    color: #fff;
    border-right: 4px solid #fff;
}

.side-bar ul li a i {
    margin-right: 15px;
    font-size: 18px;
    width: 20px;
    text-align: center;
}

/* Main Content Area */
.main-content {
    margin-left: 250px;
    margin-top: 60px;
    padding: 30px;
    width: calc(100% - 250px);
    min-height: calc(100vh - 60px);
}

/* Section Styles */
.section-1 {
    display: none;
    background: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    animation: fadeIn 0.3s ease;
}

.section-1.active-section {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.section-1 h1 {
    color: #2c3e50;
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 30px;
    text-align: center;
    border-bottom: 3px solid #667eea;
    padding-bottom: 15px;
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card i {
    font-size: 40px;
    margin-bottom: 15px;
    opacity: 0.8;
}

.stat-card h3 {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 10px;
}

.stat-number {
    font-size: 36px;
    font-weight: 700;
    margin: 0;
}

/* Form Styles */
.form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    background: #f8f9fa;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    background: #fff;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: #667eea;
    color: #fff;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-warning {
    background: #f39c12;
    color: #fff;
    margin-right: 10px;
}

.btn-warning:hover {
    background: #e67e22;
}

.btn-danger {
    background: #e74c3c;
    color: #fff;
}

.btn-danger:hover {
    background: #c0392b;
}

/* Category Section */
.category-list {
    margin-bottom: 30px;
}

.category-list h3 {
    color: #2c3e50;
    font-size: 20px;
    margin-bottom: 15px;
}

.category-items {
    list-style: none;
    padding: 0;
}

.category-items li {
    background: #f8f9fa;
    padding: 12px 20px;
    margin-bottom: 8px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    font-weight: 500;
    color: #2c3e50;
}

.add-category {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 10px;
    border: 2px dashed #667eea;
}

.add-category h3 {
    color: #667eea;
    margin-bottom: 20px;
}

/* Settings Section */
.settings-content {
    display: grid;
    gap: 30px;
}

.setting-item {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.setting-item h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 20px;
}

.setting-item p {
    margin-bottom: 10px;
    color: #555;
    font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .side-bar {
        transform: translateX(-100%);
    }

    .main-content {
        margin-left: 0;
        width: 100%;
        padding: 20px;
    }

    #checkbox:checked ~ .body .side-bar {
        transform: translateX(0);
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }
}

/* Checkbox for mobile menu */
#checkbox {
    display: none;
}

#navbtn {
    cursor: pointer;
    font-size: 20px;
    color: #667eea;
}

/* Legacy styles for compatibility */
.centre_txt {
    margin: 0 auto;
    font-size: 18px;
    text-align: center;
    max-width: 600px;
}

/* Override text-align for dashboard */
.main-content * {
    text-align: left;
}

.main-content .section-1 h1,
.main-content .stat-card,
.main-content .add-category h3 {
    text-align: center;
}