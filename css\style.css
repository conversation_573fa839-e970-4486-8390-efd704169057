* {
	padding: 0;
	margin: 0;
	box-sizing: border-box;
	font-family: arial, sans-serif;
}
.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 30px;
	background: #23242b;
	color: #fff;
}
.u-name {
	font-size: 20px;
	padding-left: 17px;
}
.u-name b {
	color: #127b8e;
}
.header i {
	font-size: 30px;
	cursor: pointer;
	color: #fff;
}
.header i:hover {
	color: #127b8e;
}
.user-p {
	text-align: center;
	padding-left: 10px;
	padding-top: 25px;
}
.user-p img {
	width: 100px;
	border-radius: 50%;
}
.user-p h4 {
	color: #ccc;
	padding: 5px 0;

}
.side-bar {
	width: 250px;
	background: #262931;
	min-height: 100vh;
	transition: 500ms width;
}
.body {
	display: flex;
}
/* Dashboard Main Content */
.main-content {
	margin-left: 250px;
	padding: 30px;
	width: calc(100% - 250px);
	min-height: calc(100vh - 60px);
	background: #f8f9fa;
}

/* Dashboard Sections */
.section-1 {
	display: none !important;
	background: #fff;
	border-radius: 10px;
	padding: 30px;
	box-shadow: 0 5px 15px rgba(0,0,0,0.1);
	margin-bottom: 20px;
	width: 100%;
	position: relative;
}

.section-1.active-section {
	display: block !important;
	visibility: visible !important;
	opacity: 1 !important;
}

.section-1 h1 {
	color: #2c3e50;
	font-size: 28px;
	font-weight: 600;
	margin-bottom: 30px;
	text-align: center;
	border-bottom: 3px solid #667eea;
	padding-bottom: 15px;
}

/* Dashboard Stats */
.dashboard-stats {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	gap: 20px;
	margin-top: 30px;
}

.stat-card {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	padding: 30px;
	border-radius: 10px;
	text-align: center;
	box-shadow: 0 5px 15px rgba(0,0,0,0.1);
	transition: transform 0.3s ease;
}

.stat-card:hover {
	transform: translateY(-5px);
}

.stat-card i {
	font-size: 40px;
	margin-bottom: 15px;
	opacity: 0.8;
}

.stat-card h3 {
	font-size: 18px;
	font-weight: 500;
	margin-bottom: 10px;
}

.stat-number {
	font-size: 36px;
	font-weight: 700;
	margin: 0;
}
.side-bar ul {
	padding-left: 18px;
	margin-top: 20px;
	list-style: none;
}

.side-bar ul li {
	font-size: 16px;
	padding: 16px 0px;
	padding-left: 0px;
	transition: 500ms;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.side-bar ul li:hover {
	background: #127b8e;
}

.side-bar ul li a {
	text-decoration: none;
	color: #eee;
	cursor: pointer;
	letter-spacing: 1px;
}

.side-bar ul li a i {
	display: inline-block;
	padding-right: 10px;
	font-size: 23px;
}

#navbtn {
	display: inline-block;
	margin-left: 70px;
	font-size: 20px;
	transition: 500ms color;
}

#checkbox {
	display: none;
}

#checkbox:checked ~ .body .side-bar {
	width: 60px;
}

#checkbox:checked ~ .body .side-bar .user-p{
	visibility: hidden;
}

#checkbox:checked ~ .body .side-bar a span{
	display: none;
}

/* Form Styles */
.form-label {
	font-weight: 600;
	color: #2c3e50;
	margin-bottom: 8px;
	display: block;
}

.form-control {
	width: 100%;
	padding: 12px 15px;
	border: 2px solid #e9ecef;
	border-radius: 8px;
	font-size: 16px;
	transition: border-color 0.3s ease;
	background: #f8f9fa;
	margin-bottom: 15px;
}

.form-control:focus {
	outline: none;
	border-color: #667eea;
	background: #fff;
}

.btn {
	padding: 12px 30px;
	border: none;
	border-radius: 8px;
	font-size: 16px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	text-decoration: none;
	display: inline-block;
}

.btn-primary {
	background: #667eea;
	color: #fff;
}

.btn-primary:hover {
	background: #5a6fd8;
	transform: translateY(-2px);
}

.btn-warning {
	background: #f39c12;
	color: #fff;
	margin-right: 10px;
}

.btn-warning:hover {
	background: #e67e22;
}

.btn-danger {
	background: #e74c3c;
	color: #fff;
}

.btn-danger:hover {
	background: #c0392b;
}

/* Category Section */
.category-list {
	margin-bottom: 30px;
}

.category-list h3 {
	color: #2c3e50;
	font-size: 20px;
	margin-bottom: 15px;
}

.category-items {
	list-style: none;
	padding: 0;
}

.category-items li {
	background: #f8f9fa;
	padding: 12px 20px;
	margin-bottom: 8px;
	border-radius: 8px;
	border-left: 4px solid #667eea;
	font-weight: 500;
	color: #2c3e50;
}

.add-category {
	background: #f8f9fa;
	padding: 25px;
	border-radius: 10px;
	border: 2px dashed #667eea;
}

.add-category h3 {
	color: #667eea;
	margin-bottom: 20px;
}

/* Settings Section */
.settings-content {
	display: grid;
	gap: 30px;
}

.setting-item {
	background: #f8f9fa;
	padding: 25px;
	border-radius: 10px;
	border-left: 4px solid #667eea;
}

.setting-item h3 {
	color: #2c3e50;
	margin-bottom: 15px;
	font-size: 20px;
}

.setting-item p {
	margin-bottom: 10px;
	color: #555;
	font-size: 16px;
}

/* Navigation Active States */
.side-bar ul li a.active {
	background: #667eea;
	color: #fff;
	border-right: 4px solid #fff;
}

.side-bar ul li a:hover {
	background: #34495e;
	color: #fff;
}
