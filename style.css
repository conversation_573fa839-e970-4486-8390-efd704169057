@import url('https://fonts.googleapis.com/css?family=Poppins:wght@480;500;700&display=swap');


*{margin: 0;
padding: 0;
box-sizing: border-box;
font-family: "Poppins", serif;}

body {
min-height: 100vh;
background: linear-gradient(to right, #e2e2e2, #c9d6ff);
color: #333;}

.container{display: flex;
    justify-content: center;
    height: 100vh;
align-items: center;}

.form-box {
width: 100%;
max-width: 450px;
padding: 30px;
background:#fff;
border-radius: 10px;
box-shadow: 0 10px rgba(0, 0, 0, 0.1);
display: none;}

h2 {
font-size: 34px;
text-align: center;
margin-bottom: 20px;}

input, select {
width: 100%;
padding: 12px;
background:#eee;
border-radius: 6px;
border: none;
outline: none;
font-size: 16px;
color:#333;
margin-bottom: 20px;}

button{
width: 100%;
padding: 12px;
background:#7494ec;
border-radius: 6px;
border: none;
cursor: pointer;
font-size: 16px;
color:#fff;
font-weight: 500;
margin-bottom: 20px;
transition: 0.5s;
}

button:hover{
background: #6884d3;}

p{font-size: 14.5px;
text-align: center;
margin-bottom: 10px;
}

p a{color: #7494ec;
text-decoration: none;}

p a:hover{
text-decoration: underline;}

.form-box.active{display: block;}

.error-message {
padding: 12px;
background:#f8d7da;
border-radius: 6px;
font-size: 16px;
color: #a42834;
text-align: center;
margin-bottom: 20px;
}

H1{font-size: 30px;
text-align: center;
margin-bottom: 20px;
color: #333;
font-weight: 500;
}

span{color:#7494ec;}

.box{margin-top: 200px;}

.box p{font-size: 22px;}

.box button{display: block;
width: 300px;
margin: 0 auto;}