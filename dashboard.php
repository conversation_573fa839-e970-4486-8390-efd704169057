<?php
session_start();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header("Location: index.php");
    exit();
} 
?>


<!DOCTYPE html>
<html>
<head>
	<title>Dashboard</title>
	<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-4Q6Gf2aSP4eDXB8Miphtr37CMZZQ5oXLH2yaXMJ2w8e2ZtHTl7GptT4jmndRuHDT" crossorigin="anonymous">
    <link rel="stylesheet" href="style.css?v=1">
	<link rel="stylesheet" href="css/style.css?v=1">

</head>
<body>
	<input type="checkbox" id="checkbox">
	<header class="header">
		<h2 class="u-name">SIDE <b>BAR</b>
			<label for="checkbox">
				<i id="navbtn" class="fa fa-bars" aria-hidden="true"></i>
			</label>
		</h2>
		<i class="fa fa-user" aria-hidden="true"></i>
	</header>
	<div class="body">
		<nav class="side-bar">
			<div class="user-p">
				<img src="img/user.jpg">
				<h4><?php echo $_SESSION['name']; ?></h4>
			</div>
			<ul>
				<li>
					<a href="#">
						<i class="fa fa-desktop" aria-hidden="true"></i>
						<span>Dashboard</span>
					</a>
				</li>
				<li>
					<a href="#">
						<i class="fa fa-tasks" aria-hidden="true"></i>
						<span>Tasks</span>
					</a>
				</li>
				<li>
					<a href="#">
						<i class="fa fa-comment-o" aria-hidden="true"></i>
						<span>Category</span>
					</a>
				</li>
				<li>
					<a href="#">
						<i class="fa fa-cog" aria-hidden="true"></i>
						<span>Setting</span>
					</a>
				</li>
				<li>
					<a href="#">
						<i class="fa fa-power-off" aria-hidden="true"></i>
						<span onclick="window.location.href='logout.php'">Logout</span>
					</a>
				</li>
			</ul>
		</nav>
		
        <section class="section-1 tasks" style='display:none;'>
        <form action="tasks.php" method="post">
  <div class="mb-3">
    <label for="task" class="form-label">Task</label>
    <input type="text" name="task" class="form-control" placeholder="Task" id="task" required>
  </div>
  <div class="mb-3">
    <select name="cataory" id="" required>
        <option value="">Select a cataory</option>
        <?php
        include 'config.php';
        $query = "SELECT * FROM catagory";
        $result = $conn->query($query);
        while ($row = mysqli_fetch_assoc($result)) {
            echo "<option value='" . $row['id'] . "'>" . ucfirst($row['name']) . "</option>";
        }
        ?>

        <!-- <option value="1">-select-</option>
        <option value="2">persional life</option>
        <option value="3">public life</option>
        <option value="4">professional life</option> -->
    </select>
  </div>
  <div class="mb-3">
    <label for="duedate " name="duedate" class="form-label">Due Date</label>
    <input type="date" class="form-control" id="duedate" required>
  </div>
  <button type="submit" class="btn btn-primary">Create task</button>
</form>
        </section>
        <section class="section-1 category">
            <ul>
            <?php
        include 'config.php';
        $query = "SELECT * FROM catagory";
        $result = $conn->query($query);
        while ($row = mysqli_fetch_assoc($result)) {
            echo "<li>" . $row['id'] . ". " . ucfirst($row['name']) . "</li> <br>";
        }
        ?>
        </ul>
        <form action="tasks.php" method="post">
  <div class="mb-3">
    <label for="catagoryadd" class="form-label">Category name</label>
    <input type="text" class="form-control" id="catagoryadd" name="catagoryname" required>
  </div>
  
  <button type="submit" class="btn btn-primary" name="addcatagory">Add new category</button>
</form>
        </section>
	</div>

</body>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js" integrity="sha384-j1CDi7MgGQ12Z7Qab0qlWQ/Qqz24Gc6BM0thvEMVjHnfYGF0rmFCozFSxQBxwHKO" crossorigin="anonymous"></script>

</html>