<?php
session_start();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header("Location: index.php");
    exit();
}
?>


<!DOCTYPE html>
<html>
<head>
	<title>Dashboard</title>
	<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-4Q6Gf2aSP4eDXB8Miphtr37CMZZQ5oXLH2yaXMJ2w8e2ZtHTl7GptT4jmndRuHDT" crossorigin="anonymous">
    <link rel="stylesheet" href="style.css?v=1">
	<link rel="stylesheet" href="css/style.css?v=1">

</head>
<body>
	<input type="checkbox" id="checkbox">
	<header class="header">
		<h2 class="u-name">SIDE <b>BAR</b>
			<label for="checkbox">
				<i id="navbtn" class="fa fa-bars" aria-hidden="true"></i>
			</label>
		</h2>
		<i class="fa fa-user" aria-hidden="true"></i>
	</header>
	<div class="body">
		<nav class="side-bar">
			<div class="user-p">
				<img src="img/user.jpg">
				<h4><?php echo $_SESSION['name']; ?></h4>
			</div>
			<ul>
				<li>
					<a href="#" class="nav-link active" data-section="dashboard">
						<i class="fa fa-desktop" aria-hidden="true"></i>
						<span>Dashboard</span>
					</a>
				</li>
				<li>
					<a href="#" class="nav-link" data-section="tasks">
						<i class="fa fa-tasks" aria-hidden="true"></i>
						<span>Tasks</span>
					</a>
				</li>
				<li>
					<a href="#" class="nav-link" data-section="category">
						<i class="fa fa-comment-o" aria-hidden="true"></i>
						<span>Category</span>
					</a>
				</li>
				<li>
					<a href="#" class="nav-link" data-section="settings">
						<i class="fa fa-cog" aria-hidden="true"></i>
						<span>Settings</span>
					</a>
				</li>
				<li>
					<a href="#" onclick="window.location.href='logout.php'">
						<i class="fa fa-power-off" aria-hidden="true"></i>
						<span>Logout</span>
					</a>
				</li>
			</ul>
		</nav>

		<div class="main-content">
			<!-- Dashboard Section -->
			<section class="section-1 dashboard active-section">
				<h1>Dashboard Overview</h1>
				<div class="dashboard-stats">
					<div class="stat-card">
						<i class="fa fa-tasks"></i>
						<h3>Total Tasks</h3>
						<p class="stat-number">
							<?php
							include 'config.php';
							$query = "SELECT COUNT(*) as total FROM tasks";
							$result = $conn->query($query);
							$row = mysqli_fetch_assoc($result);
							echo $row['total'] ?? 0;
							?>
						</p>
					</div>
					<div class="stat-card">
						<i class="fa fa-comment-o"></i>
						<h3>Categories</h3>
						<p class="stat-number">
							<?php
							$query = "SELECT COUNT(*) as total FROM catagory";
							$result = $conn->query($query);
							$row = mysqli_fetch_assoc($result);
							echo $row['total'] ?? 0;
							?>
						</p>
					</div>
					<div class="stat-card">
						<i class="fa fa-user"></i>
						<h3>Welcome</h3>
						<p><?php echo $_SESSION['name']; ?></p>
					</div>
				</div>
			</section>

			<!-- Tasks Section -->
			<section class="section-1 tasks">
				<h1>Create New Task</h1>
				<form action="tasks.php" method="post">
					<div class="mb-3">
						<label for="task" class="form-label">Task</label>
						<input type="text" name="task" class="form-control" placeholder="Task" id="task" required>
					</div>
					<div class="mb-3">
						<label for="category-select" class="form-label">Category</label>
						<select name="cataory" id="category-select" class="form-control" required>
							<option value="">Select a category</option>
							<?php
							include 'config.php';
							$query = "SELECT * FROM catagory";
							$result = $conn->query($query);
							while ($row = mysqli_fetch_assoc($result)) {
								echo "<option value='" . $row['id'] . "'>" . ucfirst($row['name']) . "</option>";
							}
							?>
						</select>
					</div>
					<div class="mb-3">
						<label for="duedate" class="form-label">Due Date</label>
						<input type="date" name="duedate" class="form-control" id="duedate" required>
					</div>
					<button type="submit" class="btn btn-primary" name="addtask">Create Task</button>
				</form>
			</section>

			<!-- Category Section -->
			<section class="section-1 category">
				<h1>Manage Categories</h1>
				<div class="category-list">
					<h3>Existing Categories</h3>
					<ul class="category-items">
						<?php
						include 'config.php';
						$query = "SELECT * FROM catagory";
						$result = $conn->query($query);
						while ($row = mysqli_fetch_assoc($result)) {
							echo "<li>" . $row['id'] . ". " . ucfirst($row['name']) . "</li>";
						}
						?>
					</ul>
				</div>
				<div class="add-category">
					<h3>Add New Category</h3>
					<form action="tasks.php" method="post">
						<div class="mb-3">
							<label for="catagoryadd" class="form-label">Category Name</label>
							<input type="text" class="form-control" id="catagoryadd" name="catagoryname" required>
						</div>
						<button type="submit" class="btn btn-primary" name="addcatagory">Add New Category</button>
					</form>
				</div>
			</section>

			<!-- Settings Section -->
			<section class="section-1 settings">
				<h1>Settings</h1>
				<div class="settings-content">
					<div class="setting-item">
						<h3>Profile Information</h3>
						<p><strong>Name:</strong> <?php echo $_SESSION['name']; ?></p>
						<p><strong>Email:</strong> <?php echo $_SESSION['email']; ?></p>
						<p><strong>Role:</strong> <?php echo ucfirst($_SESSION['role']); ?></p>
					</div>
					<div class="setting-item">
						<h3>Account Actions</h3>
						<button class="btn btn-warning" onclick="alert('Change password functionality coming soon!')">Change Password</button>
						<button class="btn btn-danger" onclick="if(confirm('Are you sure you want to logout?')) window.location.href='logout.php'">Logout</button>
					</div>
				</div>
			</section>
		</div>
	</div>

</body>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js" integrity="sha384-j1CDi7MgGQ12Z7Qab0qlWQ/Qqz24Gc6BM0thvEMVjHnfYGF0rmFCozFSxQBxwHKO" crossorigin="anonymous"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get all navigation links
    const navLinks = document.querySelectorAll('.nav-link[data-section]');
    const sections = document.querySelectorAll('.section-1');

    console.log('Found', navLinks.length, 'navigation links');
    console.log('Found', sections.length, 'sections');

    // Function to show a specific section
    function showSection(sectionName) {
        console.log('Showing section:', sectionName);

        // Hide all sections using inline styles
        sections.forEach(section => {
            section.classList.remove('active-section');
            section.style.display = 'none';
        });

        // Remove active class from all nav links
        navLinks.forEach(link => {
            link.classList.remove('active');
        });

        // Show the selected section using inline styles
        const targetSection = document.querySelector('.section-1.' + sectionName);
        console.log('Target section found:', targetSection);

        if (targetSection) {
            targetSection.classList.add('active-section');
            targetSection.style.display = 'block';
            console.log('Section displayed:', sectionName);
        } else {
            console.error('Section not found:', sectionName);
        }

        // Add active class to the clicked nav link
        const activeLink = document.querySelector('.nav-link[data-section="' + sectionName + '"]');
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }

    // Add click event listeners to navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const sectionName = this.getAttribute('data-section');
            console.log('Nav link clicked:', sectionName);
            showSection(sectionName);
        });
    });

    // Show dashboard by default
    console.log('Initializing dashboard...');
    showSection('dashboard');
});
</script>

</html>